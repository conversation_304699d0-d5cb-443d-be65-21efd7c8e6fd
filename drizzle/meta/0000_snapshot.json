{"version": "5", "dialect": "mysql", "id": "3a729eea-687b-4a79-9186-97cf2653a5dd", "prevId": "********-0000-0000-0000-************", "tables": {"account": {"name": "account", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"account_id": {"name": "account_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "clusters": {"name": "clusters", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "cluster_name": {"name": "cluster_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "zone_id": {"name": "zone_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"clusters_zone_id_zones_id_fk": {"name": "clusters_zone_id_zones_id_fk", "tableFrom": "clusters", "tableTo": "zones", "columnsFrom": ["zone_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"clusters_id": {"name": "clusters_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "departments": {"name": "departments", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "dept_name": {"name": "dept_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "dept_code": {"name": "dept_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"departments_id": {"name": "departments_id", "columns": ["id"]}}, "uniqueConstraints": {"departments_dept_code_unique": {"name": "departments_dept_code_unique", "columns": ["dept_code"]}}, "checkConstraint": {}}, "members": {"name": "members", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "cluster_id": {"name": "cluster_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "department_id": {"name": "department_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "member_status": {"name": "member_status", "type": "enum('active','inactive','transferred')", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'active'"}, "join_date": {"name": "join_date", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"members_cluster_id_clusters_id_fk": {"name": "members_cluster_id_clusters_id_fk", "tableFrom": "members", "tableTo": "clusters", "columnsFrom": ["cluster_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "members_department_id_departments_id_fk": {"name": "members_department_id_departments_id_fk", "tableFrom": "members", "tableTo": "departments", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"members_id": {"name": "members_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "session": {"name": "session", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"session_id": {"name": "session_id", "columns": ["id"]}}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "columns": ["token"]}}, "checkConstraint": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "('dept_assistant')"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"user_id": {"name": "user_id", "columns": ["id"]}}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "columns": ["email"]}}, "checkConstraint": {}}, "user_permissions": {"name": "user_permissions", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "zone_id": {"name": "zone_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "cluster_id": {"name": "cluster_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "department_id": {"name": "department_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "permission_level": {"name": "permission_level", "type": "enum('read','write','admin')", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'read'"}, "granted_at": {"name": "granted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {"user_permissions_user_id_user_id_fk": {"name": "user_permissions_user_id_user_id_fk", "tableFrom": "user_permissions", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_permissions_zone_id_zones_id_fk": {"name": "user_permissions_zone_id_zones_id_fk", "tableFrom": "user_permissions", "tableTo": "zones", "columnsFrom": ["zone_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_permissions_cluster_id_clusters_id_fk": {"name": "user_permissions_cluster_id_clusters_id_fk", "tableFrom": "user_permissions", "tableTo": "clusters", "columnsFrom": ["cluster_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_permissions_department_id_departments_id_fk": {"name": "user_permissions_department_id_departments_id_fk", "tableFrom": "user_permissions", "tableTo": "departments", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_permissions_id": {"name": "user_permissions_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "verification": {"name": "verification", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verification_id": {"name": "verification_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "zones": {"name": "zones", "columns": {"id": {"name": "id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "zone_name": {"name": "zone_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "zone_code": {"name": "zone_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(now())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"zones_id": {"name": "zones_id", "columns": ["id"]}}, "uniqueConstraints": {"zones_zone_code_unique": {"name": "zones_zone_code_unique", "columns": ["zone_code"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}