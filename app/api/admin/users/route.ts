import { NextRequest, NextResponse } from "next/server"
import { auth } from "@/lib/auth"
import { db } from "@/lib/db"
import { user, userPermissions, account } from "@/lib/db/schema"
import { eq } from "drizzle-orm"
import { UserRole } from "@/lib/auth-utils"
import { nanoid } from "nanoid"

export async function GET(request: NextRequest) {
    const session = await auth.api.getSession({
        headers: request.headers
    })

    if (!session || !['super_admin', 'senior_pastor'].includes((session.user as any).role as UserRole)) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    try {
        const usersWithPermissions = await db
            .select({
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role,
                isActive: user.isActive,
            })
            .from(user)
            .where(eq(user.isActive, true))

        // Fetch permissions for each user
        const usersWithPerms = await Promise.all(
            usersWithPermissions.map(async (user) => {
                const permissions = await db
                    .select()
                    .from(userPermissions)
                    .where(eq(userPermissions.userId, user.id))

                return {
                    ...user,
                    permissions,
                }
            })
        )

        return NextResponse.json(usersWithPerms)
    } catch (error) {
        console.error("Error fetching users:", error)
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        )
    }
}

export async function POST(request: NextRequest) {
    const session = await auth.api.getSession({
        headers: request.headers
    })

    // Only super admin and senior pastor can create users
    if (!session || !['super_admin', 'senior_pastor'].includes((session.user as any).role as UserRole)) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 403 })
    }

    try {
        const body = await request.json()
        const { name, email, password, role, isActive } = body

        // Validate required fields
        if (!name || !email || !password) {
            return NextResponse.json(
                { error: "Name, email, and password are required" },
                { status: 400 }
            )
        }

        // Check if user already exists
        const existingUser = await db
            .select()
            .from(user)
            .where(eq(user.email, email))
            .limit(1)

        if (existingUser.length > 0) {
            return NextResponse.json(
                { error: "User with this email already exists" },
                { status: 409 }
            )
        }


        // Create user
        const newUser = await db.insert(user).values({
            id: nanoid(),
            name,
            email,
            role: role || 'dept_assistant',
            isActive: isActive !== false,
            createdAt: new Date(),
            updatedAt: new Date(),
        }).$returningId()

        // Create account record for Better Auth
        await db.insert(account).values({
            id: nanoid(),
            accountId: newUser[0].id,
            providerId: 'credential',
            userId: newUser[0].id,
            password: password, // Store plain password - Better Auth will hash itassword,
            createdAt: new Date(),
            updatedAt: new Date(),
        })

        // Return user without sensitive data
        const { ...userWithoutPassword } = newUser[0]
        return NextResponse.json(userWithoutPassword, { status: 201 })

    } catch (error) {
        console.error("Error creating user:", error)
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        )
    }
}