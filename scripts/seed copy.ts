import { db } from '../lib/db'
import { zones, departments, user, account, userPermissions } from '../lib/db/schema'
import bcrypt from 'bcryptjs'
import { nanoid } from 'nanoid'
import { UserRole } from '../lib/auth-utils'

async function createUserWithCredentials(userData: {
    name: string
    email: string
    password: string
    role: UserRole
}) {
    const userId = nanoid()

    await db.insert(user).values({
        id: userId,
        name: userData.name,
        email: userData.email,
        role: userData.role,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    })

    await db.insert(account).values({
        id: nanoid(),
        accountId: userId,
        providerId: 'credential',
        userId: userId,
        password: userData.password, // Store plain password - Better Auth will hash it
        createdAt: new Date(),
        updatedAt: new Date(),
    })

    return { id: userId }
}

async function seed() {
    console.log('🌱 Starting database seed...')

    // Create zones
    await db.insert(zones).values([
        { zoneName: 'Cluster 1', zoneCode: 'C1', description: 'West Rand and South areas' },
        { zoneName: 'Cluster 2', zoneCode: 'C2', description: 'Ext 9 region areas' },
        { zoneName: 'Cluster 3', zoneCode: 'C3', description: 'Mixed areas' },
        { zoneName: 'Cluster 4', zoneCode: 'C4', description: 'Extended areas' },
    ])

    // Get the created zones
    const zonesData = await db.select().from(zones)

    // Create departments
    await db.insert(departments).values([
        { deptName: 'SSWATT', deptCode: 'SSWATT' },
        { deptName: 'DnD', deptCode: 'DND' },
        { deptName: 'EOM MENS', deptCode: 'EOM_MENS' },
        { deptName: 'EOM LADIES', deptCode: 'EOM_LADIES' },
        { deptName: 'EOM Security', deptCode: 'EOM_SEC' },
        { deptName: 'EOM Music', deptCode: 'EOM_MUS' },
        { deptName: 'EOM Sound', deptCode: 'EOM_SND' },
        { deptName: 'EOM MEDIA', deptCode: 'EOM_MED' },
        { deptName: 'EOM KIDDIES', deptCode: 'EOM_KID' },
    ])

    // Get the created departments
    const deptData = await db.select().from(departments)

    // Create initial users
    console.log('👤 Creating initial users...')

    // Super Admin
    const superAdmin = await createUserWithCredentials({
        name: 'System Administrator',
        email: '<EMAIL>',
        password: 'AdminPass123!',
        role: 'super_admin'
    })

    // Senior Pastor
    const seniorPastor = await createUserWithCredentials({
        name: 'Pastor John Smith',
        email: '<EMAIL>',
        password: 'PastorPass123!',
        role: 'senior_pastor'
    })

    // Zone Ministers
    const zoneMinister1 = await createUserWithCredentials({
        name: 'Minister Sarah Johnson',
        email: '<EMAIL>',
        password: 'Minister123!',
        role: 'zone_minister'
    })

    const zoneMinister2 = await createUserWithCredentials({
        name: 'Minister David Brown',
        email: '<EMAIL>',
        password: 'Minister123!',
        role: 'zone_minister'
    })

    // Department HODs
    const musicHod = await createUserWithCredentials({
        name: 'Mary Williams',
        email: '<EMAIL>',
        password: 'Music123!',
        role: 'dept_hod'
    })

    const sswattHod = await createUserWithCredentials({
        name: 'James Wilson',
        email: '<EMAIL>',
        password: 'SSWATT123!',
        role: 'dept_hod'
    })

    // Assign permissions
    console.log('🔑 Assigning permissions...')

    // Zone Minister 1 -> Cluster 1 & 2
    await db.insert(userPermissions).values([
        {
            userId: zoneMinister1.id,
            zoneId: zonesData[0].id,
            permissionLevel: 'admin'
        },
        {
            userId: zoneMinister1.id,
            zoneId: zonesData[1].id,
            permissionLevel: 'admin'
        }
    ])

    // Zone Minister 2 -> Cluster 3 & 4
    await db.insert(userPermissions).values([
        {
            userId: zoneMinister2.id,
            zoneId: zonesData[2].id,
            permissionLevel: 'admin'
        },
        {
            userId: zoneMinister2.id,
            zoneId: zonesData[3].id,
            permissionLevel: 'admin'
        }
    ])

    // Department HODs
    await db.insert(userPermissions).values([
        {
            userId: musicHod.id,
            departmentId: deptData.find(d => d.deptCode === 'EOM_MUS')?.id,
            permissionLevel: 'admin'
        },
        {
            userId: sswattHod.id,
            departmentId: deptData.find(d => d.deptCode === 'SSWATT')?.id,
            permissionLevel: 'admin'
        }
    ])

    console.log('✅ Database seeded successfully!')
    console.log('\n📋 Created Users:')
    console.log(`Super Admin: <EMAIL> / AdminPass123!`)
    console.log(`Senior Pastor: <EMAIL> / PastorPass123!`)
    console.log(`Zone Minister 1: <EMAIL> / Minister123!`)
    console.log(`Zone Minister 2: <EMAIL> / Minister123!`)
    console.log(`Music HOD: <EMAIL> / Music123!`)
    console.log(`SSWATT HOD: <EMAIL> / SSWATT123!`)
    console.log('\n⚠️  Remember to change these passwords after first login!')
}

seed().catch(console.error)
