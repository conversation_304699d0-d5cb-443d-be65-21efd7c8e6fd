import { betterAuth } from "better-auth"
import { drizzleAdapter } from "better-auth/adapters/drizzle"
import { db } from "./db"
import * as schema from "./db/schema"

export const auth = betterAuth({
    database: drizzleAdapter(db, {
        provider: "mysql",
        schema: {
            user: schema.user,
            session: schema.session,
            account: schema.account,
            verification: schema.verification,
        }
    }),
    emailAndPassword: {
        enabled: true,
        // requireEmailVerification: true,
    },
    session: {
        expiresIn: 60 * 60 * 8, // 8 hours
        updateAge: 60 * 60 * 24, // 24 hours
        cookieCache: {
            enabled: true,
            maxAge: 60 * 5 // 5 minutes
        }
    },
    user: {
        additionalFields: {
            role: {
                type: "string",
                required: false,
                defaultValue: "dept_assistant"
            },
            isActive: {
                type: "boolean",
                required: false,
                defaultValue: true
            }
        }
    },
    plugins: []
})

export type Session = typeof auth.$Infer.Session
export type User = typeof auth.$Infer.Session["user"]