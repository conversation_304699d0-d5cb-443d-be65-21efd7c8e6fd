import { mysqlTable, text, int, timestamp, boolean, mysqlEnum, varchar } from 'drizzle-orm/mysql-core'
import { relations } from 'drizzle-orm'

// Enums
export const roleEnum = mysqlEnum('role', [
    'super_admin',
    'senior_pastor',
    'assistant_pastor',
    'zone_minister',
    'cluster_leader',
    'dept_hod',
    'dept_assistant'
])

export const permissionLevelEnum = mysqlEnum('permission_level', ['read', 'write', 'admin'])
export const memberStatusEnum = mysqlEnum('member_status', ['active', 'inactive', 'transferred'])

export const user = mysqlTable("user", {
    id: varchar("id", { length: 36 }).primaryKey(),
    name: text("name").notNull(),
    email: varchar("email", { length: 255 }).notNull().unique(),
    emailVerified: boolean("email_verified").default(false).notNull(),
    image: text("image"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
        .defaultNow()
        .$onUpdate(() => /* @__PURE__ */ new Date())
        .notNull(),
    role: text("role").default("dept_assistant"),
    isActive: boolean("is_active").default(true),
});

export const session = mysqlTable("session", {
    id: varchar("id", { length: 36 }).primaryKey(),
    expiresAt: timestamp("expires_at").notNull(),
    token: varchar("token", { length: 255 }).notNull().unique(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
        .$onUpdate(() => /* @__PURE__ */ new Date())
        .notNull(),
    ipAddress: text("ip_address"),
    userAgent: text("user_agent"),
    userId: varchar("user_id", { length: 36 })
        .notNull()
        .references(() => user.id, { onDelete: "cascade" }),
});

export const account = mysqlTable("account", {
    id: varchar("id", { length: 36 }).primaryKey(),
    accountId: text("account_id").notNull(),
    providerId: text("provider_id").notNull(),
    userId: varchar("user_id", { length: 36 })
        .notNull()
        .references(() => user.id, { onDelete: "cascade" }),
    accessToken: text("access_token"),
    refreshToken: text("refresh_token"),
    idToken: text("id_token"),
    accessTokenExpiresAt: timestamp("access_token_expires_at"),
    refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
    scope: text("scope"),
    password: text("password"),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
        .$onUpdate(() => /* @__PURE__ */ new Date())
        .notNull(),
});

export const verification = mysqlTable("verification", {
    id: varchar("id", { length: 36 }).primaryKey(),
    identifier: text("identifier").notNull(),
    value: text("value").notNull(),
    expiresAt: timestamp("expires_at").notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
        .defaultNow()
        .$onUpdate(() => /* @__PURE__ */ new Date())
        .notNull(),
});


// Zones
export const zones = mysqlTable('zones', {
    id: int('id').primaryKey().autoincrement(),
    zoneName: text('zone_name').notNull(),
    zoneCode: varchar('zone_code', { length: 50 }).unique().notNull(),
    description: text('description'),
    isActive: boolean('is_active').default(true),
    createdAt: timestamp('created_at').defaultNow(),
})

// Clusters
export const clusters = mysqlTable('clusters', {
    id: int('id').primaryKey().autoincrement(),
    clusterName: text('cluster_name').notNull(),
    zoneId: int('zone_id').notNull().references(() => zones.id),
    description: text('description'),
    isActive: boolean('is_active').default(true),
    createdAt: timestamp('created_at').defaultNow(),
})

// Departments
export const departments = mysqlTable('departments', {
    id: int('id').primaryKey().autoincrement(),
    deptName: text('dept_name').notNull(),
    deptCode: varchar('dept_code', { length: 50 }).unique().notNull(),
    description: text('description'),
    isActive: boolean('is_active').default(true),
    createdAt: timestamp('created_at').defaultNow(),
})

// User Permissions
export const userPermissions = mysqlTable('user_permissions', {
    id: int('id').primaryKey().autoincrement(),
    userId: varchar('user_id', { length: 36 }).notNull().references(() => user.id),
    zoneId: int('zone_id').references(() => zones.id),
    clusterId: int('cluster_id').references(() => clusters.id),
    departmentId: int('department_id').references(() => departments.id),
    permissionLevel: permissionLevelEnum.default('read'),
    grantedAt: timestamp('granted_at').defaultNow(),
})

// Members
export const members = mysqlTable('members', {
    id: int('id').primaryKey().autoincrement(),
    firstName: text('first_name').notNull(),
    lastName: text('last_name').notNull(),
    email: text('email'),
    phone: text('phone'),
    address: text('address'),
    clusterId: int('cluster_id').references(() => clusters.id),
    departmentId: int('department_id').references(() => departments.id),
    memberStatus: memberStatusEnum.default('active'),
    joinDate: timestamp('join_date'),
    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
})

// Relations
export const usersRelations = relations(user, ({ many }) => ({
    sessions: many(session),
    accounts: many(account),
    permissions: many(userPermissions),
}))

export const zonesRelations = relations(zones, ({ many }) => ({
    clusters: many(clusters),
    permissions: many(userPermissions),
}))

export const clustersRelations = relations(clusters, ({ one, many }) => ({
    zone: one(zones, {
        fields: [clusters.zoneId],
        references: [zones.id],
    }),
    members: many(members),
    permissions: many(userPermissions),
}))

export const departmentsRelations = relations(departments, ({ many }) => ({
    members: many(members),
    permissions: many(userPermissions),
}))

export const membersRelations = relations(members, ({ one }) => ({
    cluster: one(clusters, {
        fields: [members.clusterId],
        references: [clusters.id],
    }),
    department: one(departments, {
        fields: [members.departmentId],
        references: [departments.id],
    }),
}))