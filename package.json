{"name": "carecrm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:seed": "tsx scripts/seed.ts", "db:studio": "drizzle-kit studio"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@hookform/resolvers": "^5.2.1", "@next/env": "^15.5.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-slot": "^1.2.3", "bcryptjs": "^3.0.2", "better-auth": "^1.3.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.2", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.5", "lucide-react": "^0.544.0", "mysql2": "^3.14.5", "nanoid": "^5.1.5", "next": "15.5.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "zod": "^4.1.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5"}}